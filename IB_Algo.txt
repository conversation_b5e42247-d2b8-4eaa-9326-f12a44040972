// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
//@version=5

indicator("OR", shorttitle="Opening Range", overlay=true)
showMain = input(true,"Show main DR/iDR 09:30-10:30", group = "main New York Session from 09:30 am")

daySess = input.session("0930-1600","Session Period",options=["0930-1600","0700-1030","0720-1030","0915-1530"])
orSess = input.session("0930-1000", "Open Range", options=["0930-0945", "0930-1000", "0930-1030","0700-0800","0720-0930","1330-1600","0200-0500","1200-1330","0915-0945"])
show_extension = input.bool(true,"Show Extension","Display extended bars on the second day")

//Bars
is_newbar(sess) =>
    t = time("D", sess, "America/New_York")
    na(t[1]) and not na(t) or t[1] < t

is_session(sess) =>
    not na(time(timeframe.period, sess, "America/New_York"))

nyNewbar = is_newbar(daySess)

bool inOpenRange = is_session(orSess)
int opening_bar = na
opening_bar := opening_bar[1]

float or_high = na
float or_low = na
float or_high_ext = na
float or_low_ext = na
float or_high_ext2 = na
float or_low_ext2 = na

float last_or_high = na
float last_or_low = na


if inOpenRange and not inOpenRange[1]
    last_or_high := na
    last_or_low := na
    or_high := na
    or_low := na
    or_high_ext := na
    or_low_ext := na
    or_high_ext2 := na
    or_low_ext2 := na
    opening_bar := bar_index

else if inOpenRange and inOpenRange[1] and not inOpenRange[2]
    last_or_high := or_high[3]
    last_or_low := or_low[3]
    or_high := math.max(high,high[1])
    or_low := math.min(low,low[1])

else if inOpenRange
    or_high := math.max(or_high[1],high)
    or_low := math.min(or_low[1],low)
    last_or_high := last_or_high[1]
    last_or_low := last_or_low[1]
    or_range = or_high - or_low
    or_high_ext := or_high + or_range
    or_low_ext := or_low - or_range
    or_high_ext2 := or_high_ext + or_range
    or_low_ext2 := or_low_ext - or_range

else
    or_high := or_high[1]
    or_low := or_low[1]
    last_or_high := last_or_high[1]
    last_or_low := last_or_low[1]
    or_high_ext := or_high_ext[1]
    or_low_ext := or_low_ext[1]
    or_high_ext2 := or_high_ext2[1]
    or_low_ext2 := or_low_ext2[1]


or_high_line = plot(or_high,"Open Range High",color.maroon,2,style=plot.style_linebr)
or_low_line = plot(or_low,"Open Range LOW",color.olive,2,style=plot.style_linebr)

fill(or_high_line,or_low_line,color.new(color.gray,80))

var line center_line = na
if inOpenRange[1] and not inOpenRange
    float or_mid = or_high - or_low > 25 ? (or_high + or_low)/2 : na
    center_line := line.new(opening_bar,or_mid,bar_index,or_mid, extend=extend.right,color = color.white,style=line.style_dashed,width=1 )

if not na(center_line) and not inOpenRange[1] and inOpenRange
    center_line.set_x2(bar_index-1)
    center_line.set_extend(extend.none)


plot(or_high_ext,"Open Range High",color.aqua,1,style=plot.style_linebr)
plot(or_low_ext,"Open Range LOW",color.aqua,1,style=plot.style_linebr)



float ohe2 = close > or_high_ext ? or_high_ext2 : na
float ole2 = close < or_low_ext ? or_low_ext2 : na
plot(ohe2,"Open Range High2",color.aqua,3,style=plot.style_linebr)
plot(ole2,"Open Range LOW2",color.aqua,3,style=plot.style_linebr)


plot(show_extension?last_or_high:na,"Last Open Range High",color.maroon,1,style=plot.style_linebr)
plot(show_extension?last_or_low:na,"Last Open Range LOW",color.olive,1,style=plot.style_linebr,display=display.price_scale)


