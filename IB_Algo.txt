// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
//@version=5

indicator("OR", shorttitle="Opening Range", overlay=true)

daySess = input.session("0930-1600","Session Period",options=["0930-1600","0700-1030","0720-1030","0915-1530"])
orSess = input.session("0930-1030", "Open Range", options=["0930-0945", "0930-1000", "0930-1030","0700-0800","0720-0930","1330-1600","0200-0500","1200-1330","0915-0945"])
show_extension = input.bool(true,"Show Extension","Display extended bars on the second day")

//Bars
is_newbar(sess) =>
    t = time("D", sess, "America/New_York")
    na(t[1]) and not na(t) or t[1] < t

is_session(sess) =>
    not na(time(timeframe.period, sess, "America/New_York"))

nyNewbar = is_newbar(daySess)

bool inOpenRange = is_session(orSess)
int opening_bar = na
opening_bar := opening_bar[1]

float or_high = na
float or_low = na

float last_or_high = na
float last_or_low = na


if inOpenRange and not inOpenRange[1]
    last_or_high := na
    last_or_low := na
    or_high := na
    or_low := na
    opening_bar := bar_index

else if inOpenRange and inOpenRange[1] and not inOpenRange[2]
    last_or_high := or_high[3]
    last_or_low := or_low[3]
    or_high := math.max(high,high[1])
    or_low := math.min(low,low[1])

else if inOpenRange
    or_high := math.max(or_high[1],high)
    or_low := math.min(or_low[1],low)
    last_or_high := last_or_high[1]
    last_or_low := last_or_low[1]

else
    or_high := or_high[1]
    or_low := or_low[1]
    last_or_high := last_or_high[1]
    last_or_low := last_or_low[1]


or_high_line = plot(or_high,"Open Range High",color.gray,2,style=plot.style_linebr)
or_low_line = plot(or_low,"Open Range LOW",color.gray,2,style=plot.style_linebr)

fill(or_high_line,or_low_line,color.new(color.gray,80))

// Calculate and plot developing midpoint
float or_mid = not na(or_high) and not na(or_low) ? (or_high + or_low)/2 : na
plot(or_mid,"Open Range Midpoint",color.white,1,style=plot.style_linebr)

plot(show_extension?last_or_high:na,"Last Open Range High",color.gray,1,style=plot.style_linebr)
plot(show_extension?last_or_low:na,"Last Open Range LOW",color.gray,1,style=plot.style_linebr,display=display.price_scale)

// Vertical lines at 9:30 and 10:30
var line start_line = na
var line end_line = na

if inOpenRange and not inOpenRange[1]
    // Draw vertical line at 9:30 (start of opening range)
    start_line := line.new(bar_index, low, bar_index, high, extend=extend.both, color=color.white, style=line.style_dotted, width=1)

if inOpenRange[1] and not inOpenRange
    // Draw vertical line at 10:30 (end of opening range)
    end_line := line.new(bar_index, low, bar_index, high, extend=extend.both, color=color.white, style=line.style_dotted, width=1)
